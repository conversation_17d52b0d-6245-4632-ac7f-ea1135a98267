<template>
  <div class="p-20 bg-white shadow-lg rounded-xl">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-2xl font-bold" :style="{ color: theme.primaryColor }">
        Pending Access Requests
      </h2>
      <input
        v-model="search"
        placeholder="Search requests..."
        class="border p-2 rounded w-1/3 shadow-sm focus:outline-none focus:ring"
        :style="{
          borderColor: theme.primaryColor,
          boxShadow: `0px 0px 4px ${theme.primaryColor}`,
        }"
      />
    </div>

    <div class="overflow-x-auto rounded-lg shadow-sm">
      <table class="min-w-full bg-white border border-gray-200 rounded-lg">
        <thead :style="{ backgroundColor: theme.primaryColor, color: 'white' }">
          <tr>
            <th class="p-3 border">ID</th>
            <th class="p-3 border">Requestor</th>
            <th class="p-3 border">Department</th>
            <th class="p-3 border">Submitted Date</th>
            <th class="p-3 border">Status</th>
            <th class="p-3 border text-center">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="request in paginatedRequests"
            :key="request.id"
            class="hover:bg-gray-100 transition"
          >
            <td class="p-3 border">{{ request.id }}</td>
            <td class="p-3 border">
              {{ request.firstName }} {{ request.lastName }}
            </td>
            <td class="p-3 border">{{ request.department }}</td>
            <td class="p-3 border text-sm">{{ formatDate(request.createdAt) }}</td>
            <td
              class="p-3 border font-semibold"
              :class="statusClass(request.approvalStatus)"
            >
              {{ request.approvalStatus }}
            </td>
            <td class="p-3 border flex justify-center space-x-2">
              <button
                v-if="canApprove(request.approvalStatus)"
                @click="openApprovalModal(request)"
                class="bg-gradient-to-r from-green-400 to-green-600 text-white px-4 py-2 rounded shadow hover:scale-105 transition"
              >
                ✓ Approve
              </button>
              <button
                @click="rejectRequest(request.id)"
                class="bg-gradient-to-r from-red-400 to-red-600 text-white px-4 py-2 rounded shadow hover:scale-105 transition"
              >
                ✗ Reject
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination Controls -->
    <div class="flex justify-between items-center mt-4">
      <label class="text-sm">Items per page:</label>
      <select v-model="itemsPerPage" class="border p-2 rounded">
        <option
          v-for="option in paginationOptions"
          :key="option"
          :value="option"
        >
          {{ option }}
        </option>
      </select>
      <div>
        <button
          @click="prevPage"
          :disabled="currentPage === 1"
          class="px-4 py-2 mx-1 rounded bg-gray-300 hover:bg-gray-400 disabled:opacity-50"
          :style="{ backgroundColor: theme.primaryColor, color: 'white' }"
        >
          ◀ Previous
        </button>
        <span class="text-gray-700">
          Page {{ currentPage }} of {{ totalPages }}
        </span>
        <button
          @click="nextPage"
          :disabled="currentPage === totalPages"
          class="px-4 py-2 mx-1 rounded bg-gray-300 hover:bg-gray-400 disabled:opacity-50"
          :style="{ backgroundColor: theme.primaryColor, color: 'white' }"
        >
          Next ▶
        </button>
      </div>
    </div>

    <!-- Approval Modal with transparent backdrop -->
    <div
      v-if="showApprovalModal"
      class="fixed inset-0 flex items-center justify-center bg-transparent backdrop-blur-sm z-50"
    >
      <div class="bg-white/95 p-6 rounded-lg shadow-xl w-[600px] max-h-[90vh] overflow-y-auto">
        <h3 class="text-xl font-semibold mb-2 text-gray-800 flex items-center">
          <span :style="{ color: theme.primaryColor }">Approve Access Request</span>
          <div class="ml-auto">
            <button @click="closeApprovalModal" class="text-gray-500 hover:text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </h3>

        <!-- Approval Flow Progress Indicator -->
        <div class="mb-6 mt-4">
          <!-- Premier Kenya Workflow (with Branch Head) -->
          <div v-if="subsidiary === 'premierkenya'" class="flex items-center justify-between">
            <!-- Submitted Step -->
            <div class="flex flex-col items-center">
              <div class="w-8 h-8 rounded-full flex items-center justify-center bg-green-500 text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
              <span class="text-xs mt-1 font-medium">Submitted</span>
            </div>

            <!-- Progress Line 1 -->
            <div class="flex-1 h-1 mx-2" :style="{ backgroundColor: getProgressColor(1) }"></div>

            <!-- Branch Head Approval Step -->
            <div class="flex flex-col items-center">
              <div class="w-8 h-8 rounded-full flex items-center justify-center"
                :class="getStepClass(1)">
                <span class="text-xs font-bold">BH</span>
              </div>
              <span class="text-xs mt-1 font-medium text-center">{{ getBranchHeadTitle() }}</span>
            </div>

            <!-- Progress Line 2 -->
            <div class="flex-1 h-1 mx-2" :style="{ backgroundColor: getProgressColor(2) }"></div>

            <!-- HR Approval Step -->
            <div class="flex flex-col items-center">
              <div class="w-8 h-8 rounded-full flex items-center justify-center"
                :class="getStepClass(2)">
                <span class="text-sm font-bold">HR</span>
              </div>
              <span class="text-xs mt-1 font-medium">HR Approval</span>
            </div>

            <!-- Progress Line 3 -->
            <div class="flex-1 h-1 mx-2" :style="{ backgroundColor: getProgressColor(3) }"></div>

            <!-- IT Approval Step -->
            <div class="flex flex-col items-center">
              <div class="w-8 h-8 rounded-full flex items-center justify-center"
                :class="getStepClass(3)">
                <span class="text-sm font-bold">IT</span>
              </div>
              <span class="text-xs mt-1 font-medium">IT Approval</span>
            </div>
          </div>

          <!-- Other Subsidiaries Workflow (HR -> IT) -->
          <div v-else class="flex items-center justify-between">
            <!-- Submitted Step -->
            <div class="flex flex-col items-center">
              <div class="w-8 h-8 rounded-full flex items-center justify-center bg-green-500 text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
              <span class="text-xs mt-1 font-medium">Submitted</span>
            </div>

            <!-- Progress Line 1 -->
            <div class="flex-1 h-1 mx-2" :style="{ backgroundColor: getProgressColor(1) }"></div>

            <!-- HR Approval Step -->
            <div class="flex flex-col items-center">
              <div class="w-8 h-8 rounded-full flex items-center justify-center"
                :class="getStepClass(1)">
                <span class="text-sm font-bold">HR</span>
              </div>
              <span class="text-xs mt-1 font-medium">HR Approval</span>
            </div>

            <!-- Progress Line 2 -->
            <div class="flex-1 h-1 mx-2" :style="{ backgroundColor: getProgressColor(2, true) }"></div>

            <!-- IT Approval Step -->
            <div class="flex flex-col items-center">
              <div class="w-8 h-8 rounded-full flex items-center justify-center"
                :class="getStepClass(2)">
                <span class="text-sm font-bold">IT</span>
              </div>
              <span class="text-xs mt-1 font-medium">IT Approval</span>
            </div>
          </div>
        </div>

        <p class="text-gray-600 mb-4">
          Review the request details before approving.
        </p>

        <!-- Displaying Full Request Details -->
        <div class="bg-gray-50 p-4 rounded-lg mb-4 border border-gray-200 shadow-sm">
          <h4 class="text-lg font-semibold mb-3" :style="{ color: theme.primaryColor }">Request Details</h4>

          <div class="grid grid-cols-2 gap-4">
            <div class="col-span-2 bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-sm text-gray-500 mb-1">Requestor</p>
              <p class="font-medium">{{ selectedRequest?.firstName }} {{ selectedRequest?.lastName }}</p>
            </div>

            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-sm text-gray-500 mb-1">System Name</p>
              <p class="font-medium">{{ selectedRequest?.systemName }}</p>
            </div>

            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-sm text-gray-500 mb-1">Branch</p>
              <p class="font-medium">{{ selectedRequest?.branch }}</p>
            </div>

            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-sm text-gray-500 mb-1">Email Address</p>
              <p class="font-medium">{{ selectedRequest?.email }}</p>
            </div>

            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-sm text-gray-500 mb-1">Telephone No</p>
              <p class="font-medium">{{ selectedRequest?.telephone }}</p>
            </div>

            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-sm text-gray-500 mb-1">Department</p>
              <p class="font-medium">{{ selectedRequest?.department }}</p>
            </div>

            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-sm text-gray-500 mb-1">Access Type</p>
              <p class="font-medium">{{ selectedRequest?.accessType }}</p>
            </div>

            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-sm text-gray-500 mb-1">Role To Be Assigned</p>
              <p class="font-medium">{{ selectedRequest?.role }}</p>
            </div>

            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-sm text-gray-500 mb-1">Previous Role</p>
              <p class="font-medium">{{ selectedRequest?.previousRole || 'N/A' }}</p>
            </div>

            <div class="col-span-2 bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-sm text-gray-500 mb-1">Reason for Access</p>
              <p class="font-medium">{{ selectedRequest?.reason }}</p>
            </div>

            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-sm text-gray-500 mb-1">Submitted Date</p>
              <p class="font-medium">{{ formatDate(selectedRequest?.createdAt) }}</p>
            </div>

            <div class="bg-white p-3 rounded-md shadow-sm border border-gray-100">
              <p class="text-sm text-gray-500 mb-1">Current Status</p>
              <p class="font-medium" :class="statusClass(selectedRequest?.approvalStatus)">{{ selectedRequest?.approvalStatus }}</p>
            </div>
          </div>
        </div>

        <!-- Supporting Documents/Attachments -->
        <div v-if="selectedRequest?.attachments && selectedRequest.attachments.length > 0" class="mt-6 bg-gray-50 p-4 rounded-lg border border-gray-200 shadow-sm">
          <h4 class="text-lg font-semibold mb-3" :style="{ color: theme.primaryColor }">Supporting Documents</h4>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div
              v-for="(attachment, index) in selectedRequest.attachments"
              :key="index"
              class="bg-white p-3 rounded-md shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3 flex-1 min-w-0">
                  <!-- File Icon -->
                  <div class="flex-shrink-0">
                    <svg v-if="isImageFile(attachment.contentType)" xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <svg v-else-if="isPdfFile(attachment.contentType)" xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>

                  <!-- File Info -->
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate" :title="attachment.filename">
                      {{ attachment.filename }}
                    </p>
                    <p class="text-xs text-gray-500">
                      {{ getFileTypeLabel(attachment.contentType) }}
                    </p>
                  </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex space-x-2 flex-shrink-0">
                  <button
                    v-if="isImageFile(attachment.contentType)"
                    @click="previewImage(attachment)"
                    class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                  >
                    Preview
                  </button>
                  <button
                    @click="downloadAttachment(attachment)"
                    class="px-3 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
                  >
                    Download
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Upload Signature -->
        <div class="mt-6 bg-gray-50 p-4 rounded-lg border border-gray-200 shadow-sm">
          <h4 class="text-lg font-semibold mb-3" :style="{ color: theme.primaryColor }">Approval Signature</h4>

          <div class="flex items-center space-x-4">
            <div class="flex-1">
              <p class="text-gray-600 mb-2">Please upload your signature to approve this request:</p>
              <div
                class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer hover:border-blue-500 transition-colors"
                @click="triggerFileInput"
              >
                <input
                  type="file"
                  ref="signatureInput"
                  accept="image/*"
                  @change="handleSignatureUpload"
                  class="hidden"
                />
                <div v-if="!signatureFile" class="py-6">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <p class="mt-2 text-sm text-gray-500">Click to upload signature</p>
                  <p class="text-xs text-gray-400">PNG, JPG, GIF up to 10MB</p>
                </div>
                <div v-else class="py-2">
                  <img
                    :src="signaturePreview"
                    alt="Signature Preview"
                    class="max-h-24 mx-auto object-contain"
                  />
                  <p class="mt-2 text-sm text-gray-500">{{ signatureFile.name }}</p>
                  <button
                    @click.stop="removeSignature"
                    class="mt-1 text-xs text-red-500 hover:text-red-700"
                  >
                    Remove
                  </button>
                </div>
              </div>
            </div>

            <div v-if="isProcessing" class="flex-shrink-0 w-32 flex flex-col items-center justify-center">
              <div class="w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-2"></div>
              <p class="text-sm text-gray-600">Processing...</p>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3 mt-6">
          <button
            @click="closeApprovalModal"
            class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            Cancel
          </button>
          <button
            @click="approveRequest"
            class="px-6 py-2 rounded-md text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            :style="{ backgroundColor: theme.primaryColor }"
            :disabled="!signatureFile || isProcessing"
            :class="{'opacity-50 cursor-not-allowed': !signatureFile || isProcessing}"
          >
            <span v-if="!isProcessing">Submit Approval</span>
            <span v-else>Processing...</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getAllAccessRequests,
  approveHR,
  approveIT,
  rejectAccessRequest,
} from "@/services/apiService";
import { subsidiaries } from "@/config/subsidiaries";
import { formatDateForDisplay } from "@/utils/dateUtils";

export default {
  props: ["subsidiary"],
  computed: {
    theme() {
      return subsidiaries[this.subsidiary];
    },
    filteredRequests() {
      return this.requests.filter(
        (request) =>
          request.firstName.toLowerCase().includes(this.search.toLowerCase()) ||
          request.lastName.toLowerCase().includes(this.search.toLowerCase()) ||
          request.department.toLowerCase().includes(this.search.toLowerCase())
      );
    },
    totalPages() {
      return Math.ceil(this.filteredRequests.length / this.itemsPerPage);
    },
    paginatedRequests() {
      const start = (this.currentPage - 1) * this.itemsPerPage;
      return this.filteredRequests.slice(start, start + this.itemsPerPage);
    },
    paginationOptions() {
      return Array.from(
        { length: Math.ceil(this.requests.length / 10) },
        (_, i) => (i + 1) * 10
      );
    },
  },
  data() {
    return {
      requests: [],
      showApprovalModal: false,
      selectedRequest: null,
      search: "",
      itemsPerPage: 10,
      currentPage: 1,
      approver: { name: "", department: "", signature: "" },
      signatureFile: null,
      signaturePreview: null,
      isProcessing: false,
    };
  },
  async created() {
    console.log("Component created with subsidiary:", this.subsidiary);
    await this.fetchRequests();
  },
  methods: {
    async fetchRequests() {
      try {
        console.log("Fetching requests for subsidiary:", this.subsidiary);
        const data = await getAllAccessRequests(this.subsidiary);
        console.log("Received requests data:", data);
        this.requests = data;
      } catch (error) {
        console.error("Error fetching requests:", error);

        // Show error notification
        const notificationEl = document.createElement('div');
        notificationEl.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
        notificationEl.innerHTML = `
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <span>Error loading requests. Please try again.</span>
          </div>
        `;
        document.body.appendChild(notificationEl);
        setTimeout(() => {
          notificationEl.remove();
        }, 3000);
      }
    },
    prevPage() {
      if (this.currentPage > 1) {
        this.currentPage--;
      }
    },
    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++;
      }
    },
    statusClass(status) {
      return {
        "text-green-600": status.includes("Approved"),
        "text-red-600": status.includes("Rejected"),
        "text-yellow-500": status === "Pending",
      };
    },
    canApprove(status) {
      // For premierkenya, allow approval for Pending, Approved by Branch Head, and Approved by HR
      if (this.subsidiary === 'premierkenya') {
        return ["Pending", "Approved by Branch Head", "Approved by HR"].includes(status);
      }
      // For other subsidiaries, only allow approval for Pending and Approved by HR
      return ["Pending", "Approved by HR"].includes(status);
    },
    openApprovalModal(request) {
      this.selectedRequest = request;
      this.showApprovalModal = true;
      this.signatureFile = null;
    },
    closeApprovalModal() {
      this.showApprovalModal = false;
    },
    triggerFileInput() {
      this.$refs.signatureInput.click();
    },

    handleSignatureUpload(event) {
      const file = event.target.files[0];
      if (file && file.type.startsWith("image/")) {
        this.signatureFile = file;
        // Create preview URL
        this.signaturePreview = URL.createObjectURL(file);
      } else {
        alert("Please upload a valid image file.");
      }
    },

    removeSignature() {
      this.signatureFile = null;
      this.signaturePreview = null;
    },

    getStepClass(step) {
      const status = this.selectedRequest?.approvalStatus;

      if (this.subsidiary === 'premierkenya') {
        // Premier Kenya workflow: Branch Head -> HR -> IT
        if (step === 1) { // Branch Head Approval
          if (status === 'Pending') {
            return 'bg-yellow-500 text-white'; // Current step
          } else if (status.includes('Branch Head') || status.includes('HR') || status.includes('IT')) {
            return 'bg-green-500 text-white'; // Completed step
          }
        } else if (step === 2) { // HR Approval
          if (status === 'Approved by Branch Head') {
            return 'bg-yellow-500 text-white'; // Current step
          } else if (status.includes('HR') || status.includes('IT')) {
            return 'bg-green-500 text-white'; // Completed step
          } else {
            return 'bg-gray-300 text-gray-600'; // Future step
          }
        } else if (step === 3) { // IT Approval
          if (status === 'Approved by HR') {
            return 'bg-yellow-500 text-white'; // Current step
          } else if (status.includes('IT')) {
            return 'bg-green-500 text-white'; // Completed step
          } else {
            return 'bg-gray-300 text-gray-600'; // Future step
          }
        }
      } else {
        // Other subsidiaries workflow: HR -> IT
        if (step === 1) { // HR Approval
          if (status === 'Pending') {
            return 'bg-yellow-500 text-white'; // Current step
          } else if (status.includes('HR') || status.includes('IT')) {
            return 'bg-green-500 text-white'; // Completed step
          }
        } else if (step === 2) { // IT Approval
          if (status === 'Approved by HR') {
            return 'bg-yellow-500 text-white'; // Current step
          } else if (status.includes('IT')) {
            return 'bg-green-500 text-white'; // Completed step
          } else {
            return 'bg-gray-300 text-gray-600'; // Future step
          }
        }
      }

      return 'bg-gray-300 text-gray-600'; // Default
    },

    getProgressColor(step, isDirectToIT = false) {
      const status = this.selectedRequest?.approvalStatus;

      if (this.subsidiary === 'premierkenya') {
        // Premier Kenya workflow: Submitted -> Branch Head -> HR -> IT
        if (step === 1) { // Progress between Submitted and Branch Head
          return status === 'Pending' ? '#EAB308' : '#22C55E';
        } else if (step === 2) { // Progress between Branch Head and HR
          if (status === 'Pending') {
            return '#D1D5DB'; // Gray
          } else if (status === 'Approved by Branch Head') {
            return '#EAB308'; // Yellow for current
          } else if (status.includes('HR') || status.includes('IT')) {
            return '#22C55E'; // Green for completed
          } else {
            return '#D1D5DB'; // Gray
          }
        } else if (step === 3) { // Progress between HR and IT
          if (status === 'Pending' || status === 'Approved by Branch Head') {
            return '#D1D5DB'; // Gray
          } else if (status === 'Approved by HR') {
            return '#EAB308'; // Yellow for current
          } else if (status.includes('IT')) {
            return '#22C55E'; // Green for completed
          } else {
            return '#D1D5DB'; // Gray
          }
        }
      } else {
        // Other subsidiaries workflow: Submitted -> HR -> IT
        // For the direct HR to IT path
        if (isDirectToIT) {
          if (status === 'Pending') {
            return '#D1D5DB'; // Gray
          } else if (status === 'Approved by HR' || status.includes('IT')) {
            return status === 'Approved by HR' ? '#EAB308' : '#22C55E'; // Yellow for current, Green for completed
          } else {
            return '#D1D5DB'; // Gray
          }
        }

        // Standard path
        if (step === 1) { // Progress between Submitted and HR
          return status === 'Pending' ? '#EAB308' : '#22C55E';
        } else if (step === 2) { // Progress between HR and IT
          if (status === 'Pending') {
            return '#D1D5DB'; // Gray
          } else if (status === 'Approved by HR' || status.includes('IT')) {
            return status === 'Approved by HR' ? '#EAB308' : '#22C55E'; // Yellow for current, Green for completed
          } else {
            return '#D1D5DB'; // Gray
          }
        }
      }

      return '#D1D5DB'; // Default gray
    },
    getBranchHeadTitle() {
      // Determine branch head title based on the selected request's branch
      const branch = this.selectedRequest?.branch?.toLowerCase() || '';

      if (branch.startsWith('govt') || branch.includes('call center')) {
        return 'Head of Checkoff';
      } else if (branch.includes('head office') || branch.includes('karen')) {
        return 'Head of Finance';
      } else {
        return 'Head of SME';
      }
    },
    formatDate(date) {
      // Format date with correct timezone for the subsidiary
      return formatDateForDisplay(date, this.subsidiary);
    },
    async approveBranchHead(requestId, subsidiary) {
      try {
        const response = await fetch(`http://localhost:7081/user-access/v1/access-request/${requestId}/approve-branch-head`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'x-subsidiary': subsidiary,
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to approve request');
        }

        return await response.json();
      } catch (error) {
        console.error('Error approving branch head:', error);
        throw error;
      }
    },
    async convertToBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = (error) => reject(error);
        reader.readAsDataURL(file);
      });
    },
    async approveRequest() {
      if (!this.selectedRequest) return;

      try {
        if (!this.signatureFile) {
          // Using a more user-friendly notification instead of alert
          const notificationEl = document.createElement('div');
          notificationEl.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
          notificationEl.innerHTML = `
            <div class="flex items-center">
              <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
              </svg>
              <span>Please upload a signature before approving.</span>
            </div>
          `;
          document.body.appendChild(notificationEl);
          setTimeout(() => {
            notificationEl.remove();
          }, 3000);
          return;
        }

        // Set processing state
        this.isProcessing = true;

        // Convert signature to base64
        const base64Signature = await this.convertToBase64(this.signatureFile);
        this.approver.signature = base64Signature;

        console.log("Approving request for ID:", this.selectedRequest.id);

        // Determine which approval endpoint to call based on current status and subsidiary
        try {
          if (this.subsidiary === 'premierkenya') {
            // Premier Kenya workflow: Branch Head -> HR -> IT
            if (this.selectedRequest.approvalStatus === "Pending") {
              console.log(`Calling approveBranchHead for request ID: ${this.selectedRequest.id}, subsidiary: ${this.subsidiary}`);
              await this.approveBranchHead(this.selectedRequest.id, this.subsidiary);
            } else if (this.selectedRequest.approvalStatus === "Approved by Branch Head") {
              console.log(`Calling approveHR for request ID: ${this.selectedRequest.id}, subsidiary: ${this.subsidiary}`);
              await approveHR(this.selectedRequest.id, this.subsidiary);
            } else if (this.selectedRequest.approvalStatus === "Approved by HR") {
              console.log(`Calling approveIT for request ID: ${this.selectedRequest.id}, subsidiary: ${this.subsidiary}`);
              await approveIT(this.selectedRequest.id, this.subsidiary);
            } else {
              throw new Error(`Invalid approval status for premierkenya: ${this.selectedRequest.approvalStatus}`);
            }
          } else {
            // Other subsidiaries workflow: HR -> IT
            if (this.selectedRequest.approvalStatus === "Pending") {
              console.log(`Calling approveHR for request ID: ${this.selectedRequest.id}, subsidiary: ${this.subsidiary}`);
              await approveHR(this.selectedRequest.id, this.subsidiary);
            } else if (this.selectedRequest.approvalStatus.includes("HR")) {
              console.log(`Calling approveIT directly after HR for request ID: ${this.selectedRequest.id}, subsidiary: ${this.subsidiary}`);
              await approveIT(this.selectedRequest.id, this.subsidiary);
            } else {
              throw new Error(`Invalid approval status: ${this.selectedRequest.approvalStatus}`);
            }
          }
        } catch (approvalError) {
          console.error("Approval API error:", approvalError);
          throw approvalError;
        }

        // Show success notification
        const notificationEl = document.createElement('div');
        notificationEl.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50';
        notificationEl.innerHTML = `
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span>Request approved successfully!</span>
          </div>
        `;
        document.body.appendChild(notificationEl);
        setTimeout(() => {
          notificationEl.remove();
        }, 3000);

        // Close modal and refresh data
        this.closeApprovalModal();
        this.fetchRequests();
      } catch (error) {
        console.error("Error approving request:", error);

        // Extract error message from the response if available
        let errorMessage = "Error approving request. Please try again.";

        if (error.response && error.response.data) {
          if (error.response.data.message) {
            errorMessage = error.response.data.message;
          } else if (error.response.data.error) {
            errorMessage = error.response.data.error;
          }
        }

        console.log("Error details:", error.response?.data || error.message);

        // Show error notification
        const notificationEl = document.createElement('div');
        notificationEl.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
        notificationEl.innerHTML = `
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <span>${errorMessage}</span>
          </div>
        `;
        document.body.appendChild(notificationEl);
        setTimeout(() => {
          notificationEl.remove();
        }, 5000);
      } finally {
        // Reset processing state
        this.isProcessing = false;
      }
    },
    async rejectRequest(requestId) {
      // Show confirmation dialog
      if (!confirm("Are you sure you want to reject this request?")) {
        return;
      }

      try {
        await rejectAccessRequest(requestId, this.subsidiary);

        // Show success notification
        const notificationEl = document.createElement('div');
        notificationEl.className = 'fixed top-4 right-4 bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded z-50';
        notificationEl.innerHTML = `
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <span>Request rejected successfully.</span>
          </div>
        `;
        document.body.appendChild(notificationEl);
        setTimeout(() => {
          notificationEl.remove();
        }, 3000);

        this.fetchRequests();
      } catch (error) {
        console.error("Error rejecting request:", error);

        // Show error notification
        const notificationEl = document.createElement('div');
        notificationEl.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
        notificationEl.innerHTML = `
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <span>Error rejecting request. Please try again.</span>
          </div>
        `;
        document.body.appendChild(notificationEl);
        setTimeout(() => {
          notificationEl.remove();
        }, 3000);
      }
    },

    // Attachment helper methods
    isImageFile(contentType) {
      return contentType && contentType.startsWith('image/');
    },

    isPdfFile(contentType) {
      return contentType && contentType.includes('pdf');
    },

    getFileTypeLabel(contentType) {
      if (!contentType) return 'Unknown';

      if (contentType.startsWith('image/')) {
        return 'Image';
      } else if (contentType.includes('pdf')) {
        return 'PDF Document';
      } else if (contentType.includes('word') || contentType.includes('document')) {
        return 'Word Document';
      } else if (contentType.includes('excel') || contentType.includes('spreadsheet')) {
        return 'Excel Spreadsheet';
      } else if (contentType.includes('text')) {
        return 'Text Document';
      } else {
        return 'Document';
      }
    },

    previewImage(attachment) {
      // Create a modal to preview the image
      const modal = document.createElement('div');
      modal.className = 'fixed inset-0 flex items-center justify-center bg-black bg-opacity-75 z-50';
      modal.innerHTML = `
        <div class="relative max-w-4xl max-h-full p-4">
          <button class="absolute top-2 right-2 text-white hover:text-gray-300 z-10" onclick="this.parentElement.parentElement.remove()">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
          <img src="data:${attachment.contentType};base64,${attachment.content}"
               alt="${attachment.filename}"
               class="max-w-full max-h-full object-contain rounded-lg shadow-lg" />
          <p class="text-white text-center mt-2 text-sm">${attachment.filename}</p>
        </div>
      `;

      // Close modal when clicking outside the image
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          modal.remove();
        }
      });

      document.body.appendChild(modal);
    },

    downloadAttachment(attachment) {
      try {
        // Create a blob from the base64 content
        const byteCharacters = atob(attachment.content);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: attachment.contentType });

        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = attachment.filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        // Show success notification
        const notificationEl = document.createElement('div');
        notificationEl.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50';
        notificationEl.innerHTML = `
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span>File downloaded successfully!</span>
          </div>
        `;
        document.body.appendChild(notificationEl);
        setTimeout(() => {
          notificationEl.remove();
        }, 3000);

      } catch (error) {
        console.error('Error downloading attachment:', error);

        // Show error notification
        const notificationEl = document.createElement('div');
        notificationEl.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
        notificationEl.innerHTML = `
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <span>Error downloading file. Please try again.</span>
          </div>
        `;
        document.body.appendChild(notificationEl);
        setTimeout(() => {
          notificationEl.remove();
        }, 3000);
      }
    },
  },
};
</script>
